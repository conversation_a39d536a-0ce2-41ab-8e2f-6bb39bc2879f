# 结直肠癌筛查微观模拟模型 产品需求文档 (PRD)

## 目标与背景

### 项目目标

本PRD旨在交付一个全面的结直肠癌筛查微观模拟模型，实现以下目标：

- **支持循证政策制定**：为中国医疗卫生部门提供科学严谨的工具，优化国家结直肠癌筛查策略
- **弥合研究与实践差距**：将复杂的流行病学研究转化为可操作的筛查政策建议
- **确立技术领先地位**：使中国在本土化癌症筛查模拟技术领域处于领先地位
- **支持卫生经济学决策**：基于中国人群数据进行筛查策略的成本效益分析
- **促进个性化筛查**：支持个体化风险评估和筛查建议

### 背景介绍

结直肠癌是全球第二大癌症死因，但通过有效筛查可高度预防。目前中国使用的筛查模型主要改编自西方人群，在中国人口统计学特征、遗传特征和医疗体系的准确性和相关性方面存在关键差距。

本项目解决了对**本土化、科学严谨的微观模拟模型**的迫切需求，该模型能够：
- 准确模拟中国人群的疾病进展
- 同时评估多种筛查策略
- 使用中国成本结构进行卫生经济学分析
- 为政策制定者提供循证筛查建议

该模型在结合**双队列架构**（自然人群队列和出生队列）、**基于机器学习的校准**和**灵活筛查策略模拟**方面实现了突破——这些能力在现有模型中尚不具备。

### 变更日志

| 版本 | 日期 | 变更内容 | 作者 |
|------|------|----------|------|
| 1.0 | 2025-01-31 | 从技术规格书重构为综合PRD文档 | 产品经理 |

## 需求规格

### 功能需求

**FR1**：系统应能模拟动态人群队列，支持可配置的初始人口统计学特征（年龄分布、性别比例、人口规模），并基于中国生命表进行自然死亡率进展模拟。

**FR2**：系统应实现双重疾病进展通路：腺瘤-癌变序列（85%病例）和锯齿状腺瘤-癌变序列（15%病例），具有不同的进展参数。

**FR3**：系统应支持个体风险因素评估，包括家族史、炎性肠病、肥胖、糖尿病、吸烟和久坐生活方式，影响腺瘤产生率。

**FR4**：系统应能模拟多种筛查工具，包括粪便免疫化学检测（FIT）、结肠镜检查、乙状结肠镜检查、风险评估问卷及其他，具有可配置的针对腺瘤和癌症不同阶段的敏感性和特异性参数。

**FR5**：系统应支持灵活的筛查策略，包括可配置的开始/结束年龄、间隔、依从性和阳性后结肠镜筛查依从性，以及贯序工具实施。

**FR6**：系统应能计算卫生经济学结果，包括直接筛查成本、治疗成本、质量调整生命年（QALY）、挽救生命年（LYG）和增量成本效益比（ICER）。

**FR7**：系统应实现基于机器学习的模型校准，使用深度神经网络和拉丁超立方抽样生成10,000个参数组合和95%置信区间。

**FR8**：系统应提供全面的数据输入管理，包括校准基准值、人口结构表、生命表和筛查工具参数。

**FR9**：系统应生成详细的模拟输出，包括年龄性别特异性腺瘤患病率、癌症发病率、死亡率和筛查策略有效性指标。

**FR10**：系统应支持长期模拟周期（最长100年）、并可自行设定筛查周期，具有季度进展周期和状态转换。

### 非功能需求

**NFR1**：系统应支持对多达100万个体进行100年周期的模拟，单次模拟运行时间不超过30分钟。

**NFR2**：系统在大规模模拟期间应将内存使用量保持在8GB以下，并支持并行计算加速。

**NFR3**：系统应提供模块化架构，能够轻松添加新的筛查工具、风险因素和进展通路，无需修改核心系统。

**NFR4**：系统应实现全面的参数验证、异常处理和详细日志记录，用于调试和审计目的。

**NFR5**：系统应保持模拟结果的一致性和可重现性，具有自动备份和恢复机制。

**NFR6**：系统应提供直观的用户界面（app、CLI、API），配有全面的文档、教程和错误消息。

**NFR7**：系统应支持多种数据输入格式（CSV、JSON、Excel），并提供数据导出功能以与外部分析工具集成。

**NFR8**：系统应实现3%年度折现率进行经济计算，并支持关键经济参数的敏感性分析。

## 用户体验设计

### 整体用户体验愿景

结直肠癌筛查微观模拟模型将提供**专业的研究级界面**，平衡复杂的分析功能与直观的可用性。系统将作为医疗政策制定者、研究人员和公共卫生官员的**可信科学工具**。

**核心用户体验原则**：
- **科学可信度**：专业界面建立对结果的信心
- **渐进式披露**：复杂参数按逻辑工作流组织
- **透明度**：清晰展示模型假设和计算过程
- **可重现性**：便于分享和记录模拟配置

### 关键交互模式

**1. 引导式模拟工作流**：分步向导设置复杂模拟，每个阶段都有验证

**2. 参数配置面板**：有组织的参数组，包含上下文帮助、默认值和范围验证

**3. 实时验证**：对参数冲突、缺失数据或无效配置提供即时反馈

**4. 结果仪表板**：交互式可视化，具有下钻功能和导出选项

**5. 场景比较**：多种筛查策略的并排比较，突出显示差异

### 核心界面和视图

**配置仪表板**：设置模拟参数、管理数据输入和访问保存配置的中央枢纽

**人群设置界面**：定义人口统计学特征、风险因素分布和队列特征的界面

**筛查策略设计器**：配置筛查协议、工具序列和依从性参数的可视化工具

**校准界面**：机器学习校准设置，包含参数范围、目标基准和收敛监控

**模拟监控器**：实时进度跟踪，显示预计完成时间和资源使用指标

**结果分析仪表板**：全面的结果可视化，包含图表、表格和统计摘要

**经济分析视图**：专门的卫生经济学结果界面，包含成本效益可视化

**导出和报告中心**：生成报告、导出数据和分享模拟配置的工具

### 无障碍访问：WCAG AA

系统将符合WCAG AA无障碍标准，确保残障研究人员的可用性，包括：
- 所有交互元素的键盘导航支持
- 具有适当ARIA标签的屏幕阅读器兼容性
- 数据可视化的高对比度配色方案
- 所有图表和图形的替代文本

### 品牌设计

**科学研究美学**：简洁、专业的界面，强调数据清晰度和科学严谨性。配色方案应传达信任和准确性，同时保持复杂数据展示的视觉层次。

## 技术方案

### 代码仓库结构：单体仓库

**设计理由**：单一仓库结构便于模拟引擎、校准模块和用户界面的集成开发，同时保持清晰的模块边界。

**结构设计**：
```
colorectal-screening-model/
├── src/
│   ├── core/           # 核心模拟引擎
│   ├── modules/        # 疾病、人群、筛查模块
│   ├── calibration/    # 机器学习校准组件
│   ├── economics/      # 卫生经济学分析
│   └── interfaces/     # app界面、CLI、API接口
├── data/              # 输入数据、基准值、生命表
├── tests/             # 综合测试套件
├── docs/              # 技术和用户文档
└── examples/          # 示例配置和教程
```

### 服务架构：模块化单体

**核心模拟引擎**：管理人群队列、疾病进展和筛查干预的中央协调器

**可插拔模块系统**：疾病模块、筛查工具和经济评估器的标准化接口

**校准服务**：基于机器学习的参数优化独立服务，支持GPU加速

**数据管理层**：支持多种输入格式和验证的统一数据访问层

### 编程语言和框架

**主要语言**：Python 3.8+，兼容科学计算生态系统并提供广泛的库支持

**核心依赖**：
- **NumPy/SciPy**：数值计算和统计函数
- **Pandas**：数据操作和分析
- **Scikit-learn**：校准用机器学习算法
- **TensorFlow/PyTorch**：高级校准用深度神经网络
- **Matplotlib/Plotly**：数据可视化和结果展示

**Web界面**：Flask/FastAPI用于REST API和Web仪表板

**数据库**：开发环境使用SQLite，生产环境使用PostgreSQL

### 测试策略：完整测试金字塔

**单元测试**：个别模块和函数的全面覆盖
**集成测试**：模块交互和数据流验证
**系统测试**：端到端模拟准确性和性能测试
**验证测试**：与已发表流行病学数据的比较

### 部署和运维

**容器化**：Docker容器确保跨环境的一致部署
**编排**：本地开发使用Docker Compose，生产扩展使用Kubernetes
**CI/CD流水线**：使用GitHub Actions进行自动化测试、验证和部署
**监控**：应用性能监控和模拟结果验证

## 史诗概览

### 史诗1：基础设施和核心模拟引擎

建立项目基础设施并实现能够管理人群队列和基本疾病进展的核心微观模拟引擎。此史诗交付具有基本功能的工作模拟框架，同时建立所有开发、测试和部署基础设施。

**关键交付物**：项目设置、核心数据结构、基本人群模拟、CI/CD流水线、初始Web界面

### 史诗2：疾病进展建模系统

实现全面的疾病自然史建模，包括腺瘤-癌变和锯齿状腺瘤通路，具有可配置的进展参数。此史诗交付具有准确疾病建模的模拟科学核心。

**关键交付物**：双通路疾病进展、风险因素集成、进展参数管理、疾病状态转换

### 史诗3：筛查策略模拟引擎

开发支持多种筛查工具、灵活策略和依从性建模的筛查模块。此史诗实现比较不同筛查方法的主要用例。

**关键交付物**：多工具筛查支持、策略配置、依从性建模、筛查结果计算

### 史诗4：卫生经济学分析模块

实现全面的卫生经济学评估，包括成本计算、QALY/LYG指标和成本效益分析。此史诗提供政策决策必需的经济分析能力。

**关键交付物**：成本建模、结果指标计算、经济分析仪表板、敏感性分析工具

### 史诗5：机器学习校准系统

开发使用深度神经网络和拉丁超立方抽样进行自动参数优化的高级校准系统。此史诗交付区别于现有解决方案的技术突破。

**关键交付物**：机器学习校准引擎、参数优化、置信区间计算、校准验证工具

### 史诗6：高级分析和报告

创建全面的结果分析、可视化和报告功能，包括场景比较和导出功能。此史诗通过专业级分析工具完善用户体验。

**关键交付物**：结果仪表板、比较分析、数据导出、报告生成、可视化工具

## 史诗1：基础设施和核心模拟引擎

**史诗目标**：为结直肠癌筛查微观模拟模型建立稳健、可扩展的基础，具备核心人群管理能力和必要的开发基础设施。此史诗交付能够管理人群队列的工作模拟框架，为后续所有开发提供基础。

### 用户故事1.1：项目基础设施搭建

作为**开发人员**，
我希望**拥有完整的项目结构和开发环境配置**，
以便**团队能够使用一致的工具和部署能力开始开发**。

#### 验收标准

1. 创建Python 3.8+项目结构，配置虚拟环境
2. 安装核心依赖（NumPy、SciPy、Pandas、pytest）
3. 初始化Git仓库，配置适当的.gitignore和README
4. 创建Docker配置用于容器化开发
5. 配置基本的CI/CD流水线使用GitHub Actions
6. 配置代码质量工具（代码检查、格式化、类型检查）

### 用户故事1.2：核心数据结构实现

作为**模拟引擎**，
我希望**拥有个体、人群和模拟状态的基础数据结构**，
以便**在整个模拟过程中表示和管理人群队列**。

#### 验收标准

1. 实现个体数据类，包含人口统计学、健康状态和历史跟踪
2. 创建人群容器类，高效管理个体
3. 实现模拟状态管理系统
4. 添加基本数据验证和错误处理
5. 为所有核心数据结构创建单元测试
6. 添加数据结构使用文档

### 用户故事1.3：基本人群初始化

作为**研究人员**，
我希望**使用可配置的人口统计学特征初始化人群队列**，
以便**设置具有真实人群特征的模拟场景**。

#### 验收标准

1. 人群初始化函数接受年龄分布、性别比例和规模参数
2. 根据指定分布生成个体人口统计学特征
3. 实现年龄和性别验证，具有适当约束
4. 实现人群统计计算函数
5. 添加人群参数配置文件支持
6. 实现基本人群摘要报告

### 用户故事1.4：生命表集成和死亡率建模

作为**模拟引擎**，
我希望**基于中国生命表应用自然死亡率**，
以便**准确建模人群老龄化和自然死亡**。

#### 验收标准

1. 实现生命表数据加载和验证系统
2. 创建年龄和性别特异性死亡率计算函数
3. 实现随机抽样的年度死亡率应用
4. 添加人群生存统计跟踪
5. 创建生命表数据格式文档
6. 实现死亡率计算单元测试

### 用户故事1.5：基本Web界面框架

作为**用户**，
我希望**拥有简单的Web界面来配置和运行基本模拟**，
以便**无需命令行工具即可与模拟系统交互**。

#### 验收标准

1. 设置Flask/FastAPI Web框架，包含基本路由
2. 创建用于人群配置的简单HTML界面
3. 实现模拟初始化和状态的API端点
4. 添加基本表单验证和错误处理
5. 实现简单结果显示页面
6. Web界面可通过Docker容器访问

## 史诗2：疾病进展建模系统

**史诗目标**：实现科学准确的疾病自然史建模，包含双重进展通路（腺瘤-癌变和锯齿状腺瘤通路）和个体风险因素集成。此史诗交付核心科学功能，实现结直肠癌发展和进展的准确模拟。

### 用户故事2.1：风险因素管理系统

作为**研究人员**，
我希望**定义和管理具有可配置权重的个体风险因素**，
以便**基于已确立的流行病学因素建模个性化疾病风险**。

#### 验收标准

1. 实现风险因素枚举系统（家族史、IBD、肥胖、糖尿病、吸烟、久坐生活方式）
2. 个体风险因素分配，支持布尔值和连续值
3. 风险因素权重配置系统，具有基于文献的默认值
4. 实现综合风险评分计算函数
5. 添加风险因素验证和约束检查
6. 包含风险因素影响文档和参考文献

### 用户故事2.2：腺瘤-癌变通路实现

作为**模拟引擎**，
我希望**使用可配置参数建模腺瘤-癌变进展通路**，
以便**准确模拟结直肠癌发展的主要路径（85%的病例）**。

#### 验收标准

1. 实现疾病状态枚举（正常→低风险腺瘤→高风险腺瘤→临床前癌症→临床癌症）
2. 基于年龄、性别和风险因素的乙状函数腺瘤产生
3. 腺瘤进展的正态分布进展建模
4. 每个疾病状态的停留时间建模
5. 实现性别特异性进展倍数
6. 解剖位置分配（近端结肠、远端结肠、直肠）

### 用户故事2.3：锯齿状腺瘤通路实现

作为**模拟引擎**，
我希望**建模锯齿状腺瘤-癌变进展通路**，
以便**模拟结直肠癌发展的替代路径（15%的病例）**。

#### 验收标准

1. 实现锯齿状腺瘤疾病状态（正常→小锯齿状→大锯齿状→临床前→临床）
2. 锯齿状腺瘤概率分配（总腺瘤病例的15%）
3. 锯齿状通路的独特进展参数
4. 与主要疾病进展引擎集成
5. 实现锯齿状腺瘤特异性进展率
6. 具有适当概率分布的通路选择逻辑

## 史诗3：筛查策略模拟引擎

**史诗目标**：开发支持多种筛查工具、灵活策略和依从性建模的筛查模块。此史诗实现比较不同筛查方法的主要用例，为政策制定者提供筛查策略评估的核心功能。

### 用户故事3.1：筛查工具配置系统

作为**研究人员**，
我希望**配置多种筛查工具及其性能参数**，
以便**准确模拟不同筛查方法的检测能力**。

#### 验收标准

1. 实现筛查工具枚举系统（FIT、结肠镜、乙状结肠镜、风险评估问卷，其他筛查工具）
2. 为每种工具配置疾病阶段特异性敏感性和特异性参数
3. 实现工具成本配置和管理系统
4. 添加筛查工具性能参数验证和范围检查
5. 创建筛查工具配置的导入/导出功能
6. 实现筛查工具性能的单元测试

### 用户故事3.2：筛查策略设计器

作为**政策制定者**，
我希望**设计灵活的筛查策略，包含多种工具组合**，
以便**评估不同筛查方案的有效性**。

#### 验收标准

1. 实现筛查策略配置界面，支持开始/结束年龄设置
2. 配置筛查间隔和实施周期的灵活设置
3. 支持顺序筛查工具实施（后续工具不早于前一工具结束）
4. 实现筛查策略模板保存和加载功能
5. 添加策略配置验证，防止逻辑冲突
6. 创建策略比较和可视化功能

### 用户故事3.3：依从性建模系统

作为**模拟引擎**，
我希望**模拟真实的筛查依从性模式**，
以便**准确反映实际筛查项目的参与率**。

#### 验收标准

1. 实现基础筛查依从性率配置（数值型）
2. 模拟阳性结果后的诊断性肠镜依从性
3. 实现依从性的时间趋势建模（首次vs重复筛查）
4. 添加依从性影响因素的权重配置
5. 创建依从性模式的统计报告功能
6. 实现依从性建模的验证测试

### 用户故事3.4：筛查结果处理引擎

作为**模拟引擎**，
我希望**处理筛查结果并触发后续行动**，
以便**模拟完整的筛查-诊断-治疗流程**。

#### 验收标准

1. 实现筛查结果判定逻辑（阳性/阴性/不确定）
2. 模拟假阳性和假阴性结果的处理
3. 实现阳性结果的后续诊断流程触发
4. 添加筛查结果对个体状态的影响建模
5. 创建筛查结果统计和跟踪系统
6. 实现筛查效果评估指标计算

## 史诗4：卫生经济学分析模块

**史诗目标**：实现全面的卫生经济学评估，包括成本计算、QALY/LYG指标和成本效益分析。此史诗提供政策决策必需的经济分析能力，支持筛查策略的经济学评价。

### 用户故事4.1：成本建模系统

作为**卫生经济学家**，
我希望**建立全面的成本计算模型**，
以便**准确评估筛查策略的经济影响**。

#### 验收标准

1. 实现筛查成本配置系统（各种筛查工具的直接成本）
2. 建立治疗成本模型（按癌症分期和治疗方案）
3. 实现间接成本计算（时间成本、交通成本等）
4. 添加成本通胀调整和年度折现率（3%）功能
5. 创建成本参数的敏感性分析工具
6. 实现成本数据的导入和验证功能

### 用户故事4.2：健康结果指标计算

作为**卫生经济学家**，
我希望**计算标准化的健康结果指标**，
以便**量化筛查策略的健康效益**。

#### 验收标准

1. 实现质量调整生命年（QALY）计算引擎
2. 计算挽救生命年（LYG）指标
3. 实现健康效用值的年龄和疾病状态调整
4. 添加生命质量权重的配置和管理
5. 创建健康结果的置信区间计算
6. 实现健康结果指标的验证测试

### 用户故事4.3：成本效益分析引擎

作为**政策制定者**，
我希望**进行全面的成本效益分析**，
以便**比较不同筛查策略的经济价值**。

#### 验收标准

1. 实现增量成本效益比（ICER）计算
2. 创建成本效益阈值分析功能
3. 实现净健康效益（NHB）计算
4. 添加成本效益可接受曲线（CEAC）生成
5. 创建成本效益分析报告模板
6. 实现多策略成本效益比较功能

### 用户故事4.4：经济分析可视化

作为**决策者**，
我希望**通过直观的图表查看经济分析结果**，
以便**快速理解不同策略的经济影响**。

#### 验收标准

1. 创建成本效益散点图和成本效益平面
2. 实现预算影响分析图表
3. 生成敏感性分析的龙卷风图
4. 创建成本分解和效益分解图表
5. 实现交互式经济分析仪表板
6. 添加经济分析结果的导出功能

## 史诗5：机器学习校准系统

**史诗目标**：开发使用深度神经网络和拉丁超立方抽样进行自动参数优化的高级校准系统。此史诗交付区别于现有解决方案的技术突破，实现模型参数的智能校准。

### 用户故事5.1：参数抽样系统

作为**校准引擎**，
我希望**生成大量参数组合用于模型校准**，
以便**全面探索参数空间并找到最优配置**。

#### 验收标准

1. 实现拉丁超立方抽样（LHS）算法
2. 生成10,000个参数组合的抽样空间
3. 实现参数约束和边界条件检查
4. 添加抽样结果的统计分析和可视化
5. 创建参数抽样的可重现性机制
6. 实现抽样效率的性能优化

### 用户故事5.2：深度神经网络训练

作为**机器学习工程师**，
我希望**训练深度神经网络进行快速参数校准**，
以便**大幅减少校准计算时间**。

#### 验收标准

1. 实现深度神经网络架构设计和配置
2. 创建训练数据集的生成和预处理流程
3. 实现网络训练的监控和早停机制
4. 添加模型性能评估和验证功能
5. 创建训练好的模型的保存和加载机制
6. 实现GPU加速训练支持

### 用户故事5.3：校准目标管理

作为**研究人员**，
我希望**设置和管理校准目标基准值**，
以便**确保模型输出与真实流行病学数据一致**。

#### 验收标准

1. 实现校准目标数据的导入和管理系统
2. 创建年龄性别特异性基准值配置
3. 实现校准目标的权重和优先级设置
4. 添加基准值数据的质量检查和验证
5. 创建校准目标与模型输出的比较功能
6. 实现校准目标的可视化展示

### 用户故事5.4：置信区间计算

作为**统计学家**，
我希望**计算校准结果的95%置信区间**，
以便**量化参数估计的不确定性**。

#### 验收标准

1. 实现Bootstrap方法的置信区间计算
2. 创建参数不确定性的传播分析
3. 实现置信区间的可视化展示
4. 添加置信区间覆盖率的验证测试
5. 创建不确定性分析报告生成功能
6. 实现置信区间计算的并行化处理

## 史诗6：高级分析和报告

**史诗目标**：创建全面的结果分析、可视化和报告功能，包括场景比较和导出功能。此史诗通过专业级分析工具完善用户体验，为决策者提供直观的结果展示。

### 用户故事6.1：结果数据管理

作为**数据分析师**，
我希望**高效管理和查询模拟结果数据**，
以便**支持复杂的分析和报告需求**。

#### 验收标准

1. 实现模拟结果的结构化存储系统
2. 创建结果数据的索引和查询优化
3. 实现结果数据的版本控制和历史追踪
4. 添加数据完整性检查和修复功能
5. 创建结果数据的备份和恢复机制
6. 实现大规模结果数据的压缩存储

### 用户故事6.2：交互式可视化仪表板

作为**决策者**，
我希望**通过交互式仪表板探索模拟结果**，
以便**深入理解不同策略的影响**。

#### 验收标准

1. 创建响应式的Web仪表板界面
2. 实现动态图表和交互式可视化组件
3. 添加筛选、排序和钻取功能
4. 创建自定义视图和个人化设置
5. 实现实时数据更新和刷新机制
6. 添加仪表板的导出和分享功能

### 用户故事6.3：场景比较分析

作为**政策分析师**，
我希望**并排比较多种筛查策略的结果**，
以便**识别最优策略和关键差异**。

#### 验收标准

1. 实现多策略结果的并排比较界面
2. 创建差异分析和统计显著性检验
3. 实现比较结果的可视化展示（热图、雷达图等）
4. 添加比较维度的自定义选择功能
5. 创建比较分析报告的自动生成
6. 实现比较结果的导出和分享功能

### 用户故事6.4：专业报告生成

作为**研究人员**，
我希望**生成专业的分析报告**，
以便**向利益相关者展示研究结果**。

#### 验收标准

1. 创建标准化的报告模板系统
2. 实现报告内容的自动填充和格式化
3. 添加图表、表格的自动嵌入功能
4. 创建报告的多格式导出（PDF、Word、HTML）
5. 实现报告的版本控制和协作编辑
6. 添加报告质量检查和验证功能

### 用户故事6.5：数据导出和集成

作为**外部分析师**，
我希望**导出模拟数据用于外部分析工具**，
以便**进行深度分析和二次研究**。

#### 验收标准

1. 实现多格式数据导出（CSV、Excel、JSON、XML）
2. 创建数据导出的自定义筛选和选择功能
3. 实现大数据集的分批导出和压缩
4. 添加导出数据的元数据和文档说明
5. 创建API接口支持程序化数据访问
6. 实现导出操作的日志记录和审计跟踪

### 用户故事6.6：性能监控和优化

作为**系统管理员**，
我希望**监控系统性能并优化分析速度**，
以便**确保大规模分析的高效执行**。

#### 验收标准

1. 实现系统性能指标的实时监控
2. 创建分析任务的资源使用跟踪
3. 实现性能瓶颈的自动识别和报警
4. 添加分析任务的优先级管理和调度
5. 创建性能优化建议的自动生成
6. 实现系统负载均衡和扩展支持

## 下一步工作

### UX专家提示

"请基于此PRD为结直肠癌筛查微观模拟模型创建全面的前端规格说明。重点关注科学研究用户体验，强调参数配置工作流、结果可视化和专业数据展示。界面应服务于需要配置复杂模拟并自信分析结果的医疗政策研究人员和公共卫生官员。"

### 架构师提示

"请基于此PRD为结直肠癌筛查微观模拟模型创建详细的技术架构文档。重点关注模块化模拟引擎设计、机器学习校准系统集成、大规模人群模拟的性能优化和可扩展部署架构。确保架构支持双通路疾病建模和灵活筛查策略模拟需求。"
