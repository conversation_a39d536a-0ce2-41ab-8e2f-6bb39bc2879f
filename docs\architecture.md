# 结直肠癌筛查微观模拟模型 全栈架构文档

## 介绍

本文档概述了结直肠癌筛查微观模拟模型的完整全栈架构，包括后端系统、前端实现及其集成。它作为AI驱动开发的单一真实来源，确保整个技术栈的一致性。

这种统一方法结合了传统上分离的后端和前端架构文档，为现代全栈应用程序简化了开发流程，其中这些关注点日益交织。

### 项目基础

**项目类型**：新建科学计算平台（Greenfield项目）
**架构模式**：微服务架构 + 模块化单体
**部署策略**：容器化部署 + 云原生架构

### 代码仓库结构

**结构选择**：Monorepo（单一仓库）
**Monorepo工具**：Nx Workspace
**包组织策略**：按功能域分离（模拟引擎、Web界面、数据管理、机器学习）

选择Monorepo的原因：
1. 模拟引擎、前端界面、数据处理模块之间需要共享复杂的类型定义
2. 科学计算代码需要在前后端之间共享算法实现
3. 统一的构建和部署流程，便于CI/CD管理
4. 代码复用和依赖管理的简化

### 高层架构图

```mermaid
graph TB
    subgraph "用户层"
        U1[医疗政策制定者]
        U2[流行病学专家]
        U3[卫生经济学家]
    end
    
    subgraph "前端层"
        WEB[Web应用<br/>React + TypeScript]
        CDN[CDN<br/>静态资源分发]
    end
    
    subgraph "API网关层"
        GW[API网关<br/>Kong/Nginx]
        AUTH[认证服务<br/>Auth0/Keycloak]
    end
    
    subgraph "应用服务层"
        API[REST API<br/>FastAPI/Python]
        SIM[模拟引擎<br/>Python/NumPy]
        ML[机器学习服务<br/>TensorFlow/PyTorch]
        DATA[数据管理服务<br/>Python/Pandas]
    end
    
    subgraph "计算层"
        QUEUE[任务队列<br/>Celery/Redis]
        WORKER[计算节点<br/>Docker容器]
        GPU[GPU集群<br/>CUDA/OpenCL]
    end
    
    subgraph "数据层"
        PG[PostgreSQL<br/>关系数据]
        REDIS[Redis<br/>缓存/会话]
        S3[对象存储<br/>文件/备份]
        TS[时序数据库<br/>InfluxDB]
    end
    
    subgraph "监控层"
        LOG[日志聚合<br/>ELK Stack]
        METRIC[指标监控<br/>Prometheus]
        TRACE[链路追踪<br/>Jaeger]
    end
    
    U1 --> WEB
    U2 --> WEB
    U3 --> WEB
    WEB --> CDN
    WEB --> GW
    GW --> AUTH
    GW --> API
    API --> SIM
    API --> ML
    API --> DATA
    SIM --> QUEUE
    ML --> QUEUE
    QUEUE --> WORKER
    WORKER --> GPU
    API --> PG
    API --> REDIS
    DATA --> S3
    WORKER --> TS
    API --> LOG
    SIM --> METRIC
    ML --> TRACE
```

## 技术栈

### 技术栈表

| 类别 | 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|------|----------|
| 前端语言 | TypeScript | 5.0+ | 类型安全的前端开发 | 科学计算需要严格的类型检查，减少运行时错误 |
| 前端框架 | React | 18.0+ | 用户界面构建 | 成熟的生态系统，优秀的性能，适合复杂数据可视化 |
| UI组件库 | 自定义组件库 | 1.0 | 科学研究界面组件 | 满足专业科学界面的特殊需求，无现成方案 |
| 状态管理 | Zustand + React Query | 4.0+ / 4.0+ | 客户端状态和服务器状态 | 轻量级状态管理，优秀的缓存和同步能力 |
| 后端语言 | Python | 3.9+ | 科学计算和API开发 | 丰富的科学计算库，NumPy/SciPy生态系统 |
| 后端框架 | FastAPI | 0.100+ | 高性能API服务 | 自动API文档，异步支持，类型提示集成 |
| 数据库 | PostgreSQL | 15+ | 主数据存储 | 强大的JSON支持，优秀的性能，ACID事务 |
| 缓存 | Redis | 7.0+ | 缓存和会话存储 | 高性能内存存储，支持复杂数据结构 |
| 对象存储 | MinIO/S3 | Latest | 文件和备份存储 | 可扩展的对象存储，支持大文件处理 |
| 时序数据库 | InfluxDB | 2.0+ | 模拟过程监控数据 | 专为时序数据优化，高效的数据压缩 |
| 任务队列 | Celery | 5.3+ | 异步任务处理 | 成熟的分布式任务队列，支持复杂工作流 |
| 消息代理 | Redis | 7.0+ | 任务队列后端 | 与缓存共用，减少基础设施复杂度 |
| 机器学习 | TensorFlow | 2.6+ | 深度学习校准 | 成熟的深度学习框架，优秀的生产支持 |
| 科学计算 | NumPy/SciPy | Latest | 数值计算 | 科学计算的标准库，优化的线性代数运算 |
| 数据处理 | Pandas | 2.0+ | 数据操作和分析 | 强大的数据处理能力，与NumPy无缝集成 |
| 可视化 | D3.js + Recharts | 7.0+ / 2.8+ | 数据可视化 | D3用于复杂图表，Recharts用于标准图表 |
| 容器化 | Docker | 24.0+ | 应用容器化 | 一致的部署环境，便于扩展和管理 |
| 编排 | Kubernetes | 1.28+ | 容器编排 | 自动扩展，服务发现，负载均衡 |
| API网关 | Kong | 3.0+ | API管理和路由 | 插件生态系统，性能监控，安全策略 |
| 认证 | Auth0 | Latest | 用户认证和授权 | 企业级安全，多因素认证，SSO支持 |
| 监控 | Prometheus + Grafana | Latest | 系统监控 | 强大的指标收集和可视化能力 |
| 日志 | ELK Stack | 8.0+ | 日志聚合和分析 | 全文搜索，日志分析，可视化仪表板 |
| CI/CD | GitHub Actions | Latest | 持续集成和部署 | 与代码仓库集成，丰富的生态系统 |
| IaC | Terraform | 1.5+ | 基础设施即代码 | 多云支持，状态管理，计划预览 |

### 架构决策记录

**ADR-001: 选择Python作为后端语言**
- 决策：使用Python 3.11+作为主要后端语言
- 理由：丰富的科学计算生态系统（NumPy、SciPy、Pandas、TensorFlow）
- 后果：需要关注性能优化，使用Cython或Numba加速关键计算

**ADR-002: 采用微服务 + 模块化单体混合架构**
- 决策：核心模拟引擎使用模块化单体，外围服务使用微服务
- 理由：模拟引擎需要紧密耦合的计算组件，外围服务需要独立扩展
- 后果：需要设计清晰的服务边界和通信协议

**ADR-003: 选择FastAPI作为API框架**
- 决策：使用FastAPI构建REST API
- 理由：自动API文档生成，优秀的性能，原生异步支持
- 后果：团队需要学习异步编程模式

## 变更日志

| 版本 | 日期 | 变更内容 | 作者 |
|------|------|----------|------|
| 1.0 | 2025-01-31 | 初始全栈架构设计 | 架构师 Winston |

## API规格说明

### REST API规格

```yaml
openapi: 3.0.0
info:
  title: 结直肠癌筛查微观模拟模型 API
  version: 1.0.0
  description: 支持大规模人群模拟、机器学习校准和卫生经济学分析的科学计算API
servers:
  - url: https://api.colorectal-screening.org/v1
    description: 生产环境
  - url: https://staging-api.colorectal-screening.org/v1
    description: 测试环境

paths:
  # 项目管理
  /projects:
    get:
      summary: 获取项目列表
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [draft, running, completed, failed]
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
      responses:
        '200':
          description: 项目列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  projects:
                    type: array
                    items:
                      $ref: '#/components/schemas/Project'
                  total:
                    type: integer
                  limit:
                    type: integer
                  offset:
                    type: integer
    post:
      summary: 创建新项目
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectCreate'
      responses:
        '201':
          description: 项目创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'

  /projects/{project_id}:
    get:
      summary: 获取项目详情
      parameters:
        - name: project_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 项目详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectDetail'

  # 模拟配置
  /projects/{project_id}/configuration:
    get:
      summary: 获取项目配置
      parameters:
        - name: project_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 项目配置
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimulationConfig'
    put:
      summary: 更新项目配置
      parameters:
        - name: project_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SimulationConfig'
      responses:
        '200':
          description: 配置更新成功

  # 模拟执行
  /projects/{project_id}/simulations:
    post:
      summary: 启动模拟
      parameters:
        - name: project_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '202':
          description: 模拟已启动
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimulationRun'

  /simulations/{simulation_id}/status:
    get:
      summary: 获取模拟状态
      parameters:
        - name: simulation_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 模拟状态
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimulationStatus'

  # 数据管理
  /data/life-tables:
    post:
      summary: 上传生命表数据
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                region:
                  type: string
                year:
                  type: integer
      responses:
        '201':
          description: 生命表上传成功

  /data/calibration-targets:
    post:
      summary: 上传校准基准值
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                data_type:
                  type: string
                  enum: [adenoma_prevalence, cancer_incidence, mortality_rate]
      responses:
        '201':
          description: 基准值上传成功

  # 机器学习校准
  /calibration/jobs:
    post:
      summary: 启动校准任务
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalibrationJob'
      responses:
        '202':
          description: 校准任务已启动

  /calibration/jobs/{job_id}/status:
    get:
      summary: 获取校准状态
      parameters:
        - name: job_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 校准状态
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalibrationStatus'

  # 结果分析
  /projects/{project_id}/results:
    get:
      summary: 获取模拟结果
      parameters:
        - name: project_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: metrics
          in: query
          schema:
            type: array
            items:
              type: string
              enum: [adenoma_prevalence, cancer_incidence, mortality_rate, qaly, lyg, icer]
      responses:
        '200':
          description: 模拟结果
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimulationResults'

components:
  schemas:
    Project:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        description:
          type: string
        status:
          type: string
          enum: [draft, running, completed, failed]
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        population_size:
          type: integer
        simulation_years:
          type: integer

    ProjectCreate:
      type: object
      required:
        - name
        - description
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 255
        description:
          type: string
          maxLength: 1000
        template_id:
          type: string
          format: uuid

    SimulationConfig:
      type: object
      properties:
        population:
          $ref: '#/components/schemas/PopulationConfig'
        disease_model:
          $ref: '#/components/schemas/DiseaseModelConfig'
        screening_strategy:
          $ref: '#/components/schemas/ScreeningStrategyConfig'
        economic_parameters:
          $ref: '#/components/schemas/EconomicConfig'
        calibration_settings:
          $ref: '#/components/schemas/CalibrationConfig'

    PopulationConfig:
      type: object
      properties:
        size:
          type: integer
          minimum: 1000
          maximum: 1000000
        age_distribution:
          type: object
          properties:
            min_age:
              type: integer
              minimum: 18
              maximum: 100
            max_age:
              type: integer
              minimum: 18
              maximum: 100
        gender_ratio:
          type: number
          minimum: 0
          maximum: 1
        risk_factors:
          type: object
          properties:
            family_history:
              type: number
              minimum: 0
              maximum: 3
            ibd:
              type: number
              minimum: 0
              maximum: 3
            obesity:
              type: number
              minimum: 0
              maximum: 3
            diabetes:
              type: number
              minimum: 0
              maximum: 3
            smoking:
              type: number
              minimum: 0
              maximum: 3
            sedentary:
              type: number
              minimum: 0
              maximum: 3

    DiseaseModelConfig:
      type: object
      properties:
        adenoma_pathway_rate:
          type: number
          minimum: 0.8
          maximum: 0.9
          default: 0.85
        serrated_pathway_rate:
          type: number
          minimum: 0.1
          maximum: 0.2
          default: 0.15
        progression_parameters:
          type: object

    ScreeningStrategyConfig:
      type: object
      properties:
        tools:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                enum: [fit, colonoscopy, sigmoidoscopy, risk_assessment]
              start_age:
                type: integer
                minimum: 40
                maximum: 80
              end_age:
                type: integer
                minimum: 40
                maximum: 80
              interval:
                type: integer
                minimum: 1
                maximum: 10
              sensitivity:
                type: object
              specificity:
                type: object
              compliance_rate:
                type: number
                minimum: 0
                maximum: 1

    SimulationRun:
      type: object
      properties:
        id:
          type: string
          format: uuid
        project_id:
          type: string
          format: uuid
        status:
          type: string
          enum: [queued, running, completed, failed]
        started_at:
          type: string
          format: date-time
        estimated_completion:
          type: string
          format: date-time

    SimulationStatus:
      type: object
      properties:
        id:
          type: string
          format: uuid
        status:
          type: string
          enum: [queued, running, completed, failed]
        progress:
          type: number
          minimum: 0
          maximum: 1
        current_year:
          type: integer
        total_years:
          type: integer
        individuals_processed:
          type: integer
        total_individuals:
          type: integer
        estimated_completion:
          type: string
          format: date-time
        resource_usage:
          type: object
          properties:
            cpu_usage:
              type: number
            memory_usage:
              type: number
            gpu_usage:
              type: number

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []
```

## 数据库模式

### 关系数据库模式 (PostgreSQL)

```sql
-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    organization VARCHAR(255),
    role VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 项目表
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) NOT NULL DEFAULT 'draft',
    owner_id UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    population_size INTEGER NOT NULL DEFAULT 10000,
    simulation_years INTEGER NOT NULL DEFAULT 100,
    config JSONB NOT NULL DEFAULT '{}'::JSONB,
    metadata JSONB
);

-- 模拟运行表
CREATE TABLE simulation_runs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL REFERENCES projects(id),
    status VARCHAR(50) NOT NULL DEFAULT 'queued',
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    progress NUMERIC(5,4) DEFAULT 0,
    current_year INTEGER DEFAULT 0,
    error_message TEXT,
    config_snapshot JSONB NOT NULL,
    resource_usage JSONB,
    worker_id VARCHAR(255)
);

-- 模拟结果表
CREATE TABLE simulation_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    simulation_id UUID NOT NULL REFERENCES simulation_runs(id),
    result_type VARCHAR(50) NOT NULL,
    data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 数据文件表
CREATE TABLE data_files (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    file_type VARCHAR(50) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    content_type VARCHAR(100) NOT NULL,
    metadata JSONB,
    uploaded_by UUID NOT NULL REFERENCES users(id),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    version INTEGER NOT NULL DEFAULT 1
);

-- 生命表数据
CREATE TABLE life_tables (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    region VARCHAR(100) NOT NULL,
    year INTEGER NOT NULL,
    gender VARCHAR(10) NOT NULL,
    file_id UUID NOT NULL REFERENCES data_files(id),
    data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(region, year, gender)
);

-- 校准基准值
CREATE TABLE calibration_targets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    target_type VARCHAR(50) NOT NULL,
    region VARCHAR(100) NOT NULL,
    year INTEGER NOT NULL,
    gender VARCHAR(10) NOT NULL,
    age_group VARCHAR(20) NOT NULL,
    value NUMERIC(10,6) NOT NULL,
    confidence_lower NUMERIC(10,6),
    confidence_upper NUMERIC(10,6),
    source VARCHAR(255),
    file_id UUID REFERENCES data_files(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 校准任务表
CREATE TABLE calibration_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL REFERENCES projects(id),
    status VARCHAR(50) NOT NULL DEFAULT 'queued',
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    progress NUMERIC(5,4) DEFAULT 0,
    config JSONB NOT NULL,
    results JSONB,
    error_message TEXT,
    worker_id VARCHAR(255)
);

-- 筛查工具参数表
CREATE TABLE screening_tools (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    tool_type VARCHAR(50) NOT NULL,
    sensitivity JSONB NOT NULL,
    specificity JSONB NOT NULL,
    cost NUMERIC(10,2) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES users(id)
);

-- 索引
CREATE INDEX idx_projects_owner ON projects(owner_id);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_simulation_runs_project ON simulation_runs(project_id);
CREATE INDEX idx_simulation_runs_status ON simulation_runs(status);
CREATE INDEX idx_simulation_results_simulation ON simulation_results(simulation_id);
CREATE INDEX idx_data_files_type ON data_files(file_type);
CREATE INDEX idx_life_tables_region_year ON life_tables(region, year);
CREATE INDEX idx_calibration_targets_type ON calibration_targets(target_type);
CREATE INDEX idx_calibration_jobs_project ON calibration_jobs(project_id);
CREATE INDEX idx_calibration_jobs_status ON calibration_jobs(status);
```

### 时序数据库模式 (InfluxDB)

```
// 模拟执行指标
measurement: simulation_metrics
tags:
  - simulation_id: UUID
  - project_id: UUID
  - metric_type: String (cpu_usage, memory_usage, gpu_usage, progress)
  - worker_id: String
fields:
  - value: Float
  - year: Integer
  - individuals_processed: Integer
timestamp: Execution time

// 疾病状态转换
measurement: disease_transitions
tags:
  - simulation_id: UUID
  - transition_type: String (normal_to_lowrisk_adenoma, lowrisk_adenoma_to_highrisk_adenoma, highrisk_adenoma_to_cancer, etc.)
  - age_group: String
  - gender: String
fields:
  - count: Integer
  - rate: Float
timestamp: Simulation time

// 筛查结果
measurement: screening_results
tags:
  - simulation_id: UUID
  - tool_type: String
  - age_group: String
  - gender: String
  - result_type: String (true_positive, false_positive, etc.)
fields:
  - count: Integer
  - rate: Float
timestamp: Simulation time
```

### 对象存储结构 (MinIO/S3)

```
/data/
  /life-tables/
    /{region}/{year}/{filename}.csv
  /calibration-targets/
    /{target_type}/{region}/{filename}.csv
  /population/
    /{project_id}/population_structure.csv
  /results/
    /{project_id}/{simulation_id}/
      summary.json
      adenoma_prevalence.csv
      cancer_incidence.csv
      mortality_rates.csv
      economic_results.csv
      charts/
        chart1.png
        chart2.png
  /models/
    /calibration/
      /{project_id}/{job_id}/
        model.h5
        parameters.json
        confidence_intervals.csv
  /backups/
    /{timestamp}/
      database_dump.sql
      config_snapshot.json
```

## 前端架构

### 组件架构

#### 组件组织结构

```
src/
├── components/                 # 可复用组件
│   ├── ui/                    # 基础UI组件
│   │   ├── Button/
│   │   ├── Input/
│   │   ├── Modal/
│   │   └── index.ts
│   ├── charts/                # 图表组件
│   │   ├── ScientificLineChart/
│   │   ├── InteractiveBarChart/
│   │   ├── SensitivityHeatmap/
│   │   └── index.ts
│   ├── forms/                 # 表单组件
│   │   ├── ParameterForm/
│   │   ├── DataUpload/
│   │   ├── ValidationMessage/
│   │   └── index.ts
│   └── layout/                # 布局组件
│       ├── Header/
│       ├── Sidebar/
│       ├── PageLayout/
│       └── index.ts
├── pages/                     # 页面组件
│   ├── Dashboard/
│   ├── SimulationConfig/
│   ├── ResultsAnalysis/
│   ├── DataManagement/
│   └── index.ts
├── features/                  # 功能模块
│   ├── simulation/
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── services/
│   │   └── types/
│   ├── calibration/
│   ├── results/
│   └── data-management/
├── hooks/                     # 自定义Hooks
│   ├── useSimulation.ts
│   ├── useCalibration.ts
│   ├── useResults.ts
│   └── index.ts
├── services/                  # API服务
│   ├── api.ts
│   ├── simulation.ts
│   ├── calibration.ts
│   └── index.ts
├── stores/                    # 状态管理
│   ├── simulation.ts
│   ├── user.ts
│   ├── ui.ts
│   └── index.ts
├── utils/                     # 工具函数
│   ├── validation.ts
│   ├── formatting.ts
│   ├── calculations.ts
│   └── index.ts
└── types/                     # TypeScript类型
    ├── simulation.ts
    ├── api.ts
    ├── ui.ts
    └── index.ts
```

#### 组件模板

```typescript
// 标准组件模板
import React from 'react';
import { cn } from '@/utils/classnames';

interface ComponentProps {
  className?: string;
  children?: React.ReactNode;
  // 其他props
}

export const Component: React.FC<ComponentProps> = ({
  className,
  children,
  ...props
}) => {
  return (
    <div className={cn('component-base-styles', className)} {...props}>
      {children}
    </div>
  );
};

Component.displayName = 'Component';

export default Component;
```

### 状态管理架构

#### 状态结构

```typescript
// 全局状态结构
interface AppState {
  // 用户状态
  user: {
    profile: UserProfile | null;
    permissions: string[];
    preferences: UserPreferences;
  };

  // UI状态
  ui: {
    theme: 'light' | 'dark';
    sidebarCollapsed: boolean;
    loading: Record<string, boolean>;
    notifications: Notification[];
  };

  // 模拟状态
  simulation: {
    currentProject: Project | null;
    configuration: SimulationConfig;
    runningSimulations: SimulationRun[];
    results: SimulationResults[];
  };

  // 校准状态
  calibration: {
    jobs: CalibrationJob[];
    currentJob: CalibrationJob | null;
    models: CalibrationModel[];
  };

  // 数据管理状态
  data: {
    lifeTables: LifeTable[];
    calibrationTargets: CalibrationTarget[];
    screeningTools: ScreeningTool[];
    uploadProgress: Record<string, number>;
  };
}
```

#### 状态管理实现

```typescript
// Zustand store示例
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

interface SimulationState {
  currentProject: Project | null;
  configuration: SimulationConfig;
  setCurrentProject: (project: Project | null) => void;
  updateConfiguration: (config: Partial<SimulationConfig>) => void;
  resetConfiguration: () => void;
}

export const useSimulationStore = create<SimulationState>()(
  devtools(
    persist(
      (set, get) => ({
        currentProject: null,
        configuration: getDefaultConfiguration(),

        setCurrentProject: (project) =>
          set({ currentProject: project }),

        updateConfiguration: (config) =>
          set((state) => ({
            configuration: { ...state.configuration, ...config }
          })),

        resetConfiguration: () =>
          set({ configuration: getDefaultConfiguration() }),
      }),
      {
        name: 'simulation-store',
        partialize: (state) => ({
          configuration: state.configuration
        }),
      }
    ),
    { name: 'simulation-store' }
  )
);
```

### 路由架构

```typescript
// 路由配置
import { createBrowserRouter } from 'react-router-dom';
import { lazy } from 'react';

// 懒加载页面组件
const Dashboard = lazy(() => import('@/pages/Dashboard'));
const SimulationConfig = lazy(() => import('@/pages/SimulationConfig'));
const ResultsAnalysis = lazy(() => import('@/pages/ResultsAnalysis'));
const DataManagement = lazy(() => import('@/pages/DataManagement'));

export const router = createBrowserRouter([
  {
    path: '/',
    element: <RootLayout />,
    children: [
      {
        index: true,
        element: <Dashboard />,
      },
      {
        path: 'simulation',
        children: [
          {
            path: 'config/:projectId?',
            element: <SimulationConfig />,
          },
          {
            path: 'monitor/:simulationId',
            element: <SimulationMonitor />,
          },
        ],
      },
      {
        path: 'results',
        children: [
          {
            path: ':projectId',
            element: <ResultsAnalysis />,
          },
          {
            path: 'compare',
            element: <ResultsComparison />,
          },
        ],
      },
      {
        path: 'data',
        element: <DataManagement />,
      },
      {
        path: 'settings',
        element: <Settings />,
      },
    ],
  },
]);
```

## 后端架构

### 服务架构

#### 微服务组织结构

```
backend/
├── api-gateway/               # API网关服务
│   ├── src/
│   │   ├── middleware/
│   │   ├── routes/
│   │   └── main.py
│   └── requirements.txt
├── simulation-service/        # 模拟引擎服务
│   ├── src/
│   │   ├── core/             # 核心模拟引擎
│   │   │   ├── population.py
│   │   │   ├── disease.py
│   │   │   ├── screening.py
│   │   │   └── economics.py
│   │   ├── models/           # 数据模型
│   │   ├── services/         # 业务逻辑
│   │   ├── workers/          # 异步任务
│   │   └── main.py
│   └── requirements.txt
├── calibration-service/       # 机器学习校准服务
│   ├── src/
│   │   ├── ml/              # 机器学习模块
│   │   │   ├── networks.py
│   │   │   ├── training.py
│   │   │   └── optimization.py
│   │   ├── data/            # 数据处理
│   │   ├── models/
│   │   └── main.py
│   └── requirements.txt
├── data-service/             # 数据管理服务
│   ├── src/
│   │   ├── storage/         # 存储管理
│   │   ├── validation/      # 数据验证
│   │   ├── processing/      # 数据处理
│   │   └── main.py
│   └── requirements.txt
├── auth-service/             # 认证授权服务
│   ├── src/
│   │   ├── auth/
│   │   ├── users/
│   │   └── main.py
│   └── requirements.txt
└── shared/                   # 共享库
    ├── database/
    ├── messaging/
    ├── monitoring/
    └── utils/
```

#### 控制器模板

```python
# FastAPI控制器模板
from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional
from uuid import UUID

from ..models import Project, ProjectCreate, ProjectUpdate
from ..services import ProjectService
from ..dependencies import get_current_user, get_project_service
from ..schemas import User

router = APIRouter(prefix="/projects", tags=["projects"])

@router.get("/", response_model=List[Project])
async def list_projects(
    skip: int = 0,
    limit: int = 20,
    status: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    project_service: ProjectService = Depends(get_project_service)
):
    """获取项目列表"""
    try:
        projects = await project_service.list_projects(
            user_id=current_user.id,
            skip=skip,
            limit=limit,
            status=status
        )
        return projects
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.post("/", response_model=Project, status_code=status.HTTP_201_CREATED)
async def create_project(
    project_data: ProjectCreate,
    current_user: User = Depends(get_current_user),
    project_service: ProjectService = Depends(get_project_service)
):
    """创建新项目"""
    try:
        project = await project_service.create_project(
            project_data=project_data,
            owner_id=current_user.id
        )
        return project
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("/{project_id}", response_model=Project)
async def get_project(
    project_id: UUID,
    current_user: User = Depends(get_current_user),
    project_service: ProjectService = Depends(get_project_service)
):
    """获取项目详情"""
    try:
        project = await project_service.get_project(
            project_id=project_id,
            user_id=current_user.id
        )
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )
        return project
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
```

### 数据访问层

#### 仓储模式实现

```python
# 数据访问层模板
from abc import ABC, abstractmethod
from typing import List, Optional
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload

from ..models import Project, ProjectCreate, ProjectUpdate
from ..database import get_session

class ProjectRepository(ABC):
    """项目仓储接口"""

    @abstractmethod
    async def create(self, project_data: ProjectCreate, owner_id: UUID) -> Project:
        pass

    @abstractmethod
    async def get_by_id(self, project_id: UUID) -> Optional[Project]:
        pass

    @abstractmethod
    async def list_by_user(
        self,
        user_id: UUID,
        skip: int = 0,
        limit: int = 20,
        status: Optional[str] = None
    ) -> List[Project]:
        pass

    @abstractmethod
    async def update(self, project_id: UUID, project_data: ProjectUpdate) -> Optional[Project]:
        pass

    @abstractmethod
    async def delete(self, project_id: UUID) -> bool:
        pass

class SQLProjectRepository(ProjectRepository):
    """PostgreSQL项目仓储实现"""

    def __init__(self, session: AsyncSession):
        self.session = session

    async def create(self, project_data: ProjectCreate, owner_id: UUID) -> Project:
        project = Project(
            **project_data.dict(),
            owner_id=owner_id
        )
        self.session.add(project)
        await self.session.commit()
        await self.session.refresh(project)
        return project

    async def get_by_id(self, project_id: UUID) -> Optional[Project]:
        stmt = select(Project).where(Project.id == project_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def list_by_user(
        self,
        user_id: UUID,
        skip: int = 0,
        limit: int = 20,
        status: Optional[str] = None
    ) -> List[Project]:
        stmt = select(Project).where(Project.owner_id == user_id)

        if status:
            stmt = stmt.where(Project.status == status)

        stmt = stmt.offset(skip).limit(limit).order_by(Project.updated_at.desc())

        result = await self.session.execute(stmt)
        return result.scalars().all()

    async def update(self, project_id: UUID, project_data: ProjectUpdate) -> Optional[Project]:
        stmt = (
            update(Project)
            .where(Project.id == project_id)
            .values(**project_data.dict(exclude_unset=True))
            .returning(Project)
        )
        result = await self.session.execute(stmt)
        await self.session.commit()
        return result.scalar_one_or_none()

    async def delete(self, project_id: UUID) -> bool:
        stmt = delete(Project).where(Project.id == project_id)
        result = await self.session.execute(stmt)
        await self.session.commit()
        return result.rowcount > 0

# 依赖注入
async def get_project_repository(
    session: AsyncSession = Depends(get_session)
) -> ProjectRepository:
    return SQLProjectRepository(session)
```

### 认证和授权

#### 认证流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端应用
    participant G as API网关
    participant A as 认证服务
    participant S as 业务服务

    U->>F: 登录请求
    F->>A: 发送凭据
    A->>A: 验证凭据
    A->>F: 返回JWT Token
    F->>F: 存储Token

    F->>G: API请求 + Token
    G->>G: 验证Token
    G->>A: 验证Token有效性
    A->>G: Token验证结果
    G->>S: 转发请求
    S->>G: 返回响应
    G->>F: 返回响应
```

#### JWT认证实现

```python
# JWT认证实现
from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

from ..config import settings
from ..models import User
from ..services import UserService

# 密码加密
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(
        to_encode,
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM
    )
    return encoded_jwt

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    user_service: UserService = Depends(get_user_service)
) -> User:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(
            credentials.credentials,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    user = await user_service.get_user_by_id(user_id)
    if user is None:
        raise credentials_exception

    return user

def require_permission(permission: str):
    """权限装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            if not current_user or permission not in current_user.permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Insufficient permissions"
                )
            return await func(*args, **kwargs)
        return wrapper
    return decorator
```

## 统一项目结构

```
colorectal-screening-model/
├── .github/                    # CI/CD工作流
│   └── workflows/
│       ├── ci.yaml
│       ├── deploy-staging.yaml
│       └── deploy-production.yaml
├── apps/                       # 应用包
│   ├── web/                    # 前端应用
│   │   ├── src/
│   │   │   ├── components/     # UI组件
│   │   │   ├── pages/          # 页面组件
│   │   │   ├── hooks/          # 自定义Hooks
│   │   │   ├── services/       # API客户端服务
│   │   │   ├── stores/         # 状态管理
│   │   │   ├── styles/         # 全局样式
│   │   │   ├── utils/          # 前端工具
│   │   │   └── types/          # TypeScript类型
│   │   ├── public/             # 静态资源
│   │   ├── tests/              # 前端测试
│   │   ├── package.json
│   │   ├── vite.config.ts
│   │   └── tsconfig.json
│   ├── api-gateway/            # API网关
│   │   ├── src/
│   │   │   ├── middleware/
│   │   │   ├── routes/
│   │   │   └── main.py
│   │   ├── tests/
│   │   ├── requirements.txt
│   │   └── Dockerfile
│   ├── simulation-service/     # 模拟引擎服务
│   │   ├── src/
│   │   │   ├── core/          # 核心模拟引擎
│   │   │   ├── models/        # 数据模型
│   │   │   ├── services/      # 业务逻辑
│   │   │   ├── workers/       # 异步任务
│   │   │   └── main.py
│   │   ├── tests/
│   │   ├── requirements.txt
│   │   └── Dockerfile
│   ├── calibration-service/    # 机器学习校准服务
│   │   ├── src/
│   │   │   ├── ml/           # 机器学习模块
│   │   │   ├── data/         # 数据处理
│   │   │   └── main.py
│   │   ├── tests/
│   │   ├── requirements.txt
│   │   └── Dockerfile
│   └── data-service/          # 数据管理服务
│       ├── src/
│       │   ├── storage/       # 存储管理
│       │   ├── validation/    # 数据验证
│       │   └── main.py
│       ├── tests/
│       ├── requirements.txt
│       └── Dockerfile
├── packages/                   # 共享包
│   ├── shared-types/          # 共享TypeScript类型
│   │   ├── src/
│   │   │   ├── api/
│   │   │   ├── simulation/
│   │   │   └── index.ts
│   │   └── package.json
│   ├── ui-components/         # 共享UI组件
│   │   ├── src/
│   │   │   ├── charts/
│   │   │   ├── forms/
│   │   │   └── index.ts
│   │   └── package.json
│   ├── python-shared/         # 共享Python库
│   │   ├── src/
│   │   │   ├── database/
│   │   │   ├── messaging/
│   │   │   ├── monitoring/
│   │   │   └── utils/
│   │   ├── setup.py
│   │   └── requirements.txt
│   └── config/                # 共享配置
│       ├── eslint/
│       ├── typescript/
│       ├── jest/
│       └── docker/
├── infrastructure/             # 基础设施即代码
│   ├── terraform/
│   │   ├── environments/
│   │   │   ├── dev/
│   │   │   ├── staging/
│   │   │   └── production/
│   │   ├── modules/
│   │   │   ├── kubernetes/
│   │   │   ├── database/
│   │   │   └── storage/
│   │   └── main.tf
│   ├── kubernetes/
│   │   ├── base/
│   │   │   ├── api-gateway/
│   │   │   ├── simulation-service/
│   │   │   ├── calibration-service/
│   │   │   └── data-service/
│   │   └── overlays/
│   │       ├── dev/
│   │       ├── staging/
│   │       └── production/
│   └── docker-compose/
│       ├── docker-compose.dev.yml
│       ├── docker-compose.staging.yml
│       └── docker-compose.prod.yml
├── scripts/                    # 构建和部署脚本
│   ├── build.sh
│   ├── deploy.sh
│   ├── test.sh
│   └── setup-dev.sh
├── docs/                       # 文档
│   ├── prd.md
│   ├── front-end-spec.md
│   ├── architecture.md
│   ├── api-docs/
│   └── deployment/
├── .env.example                # 环境变量模板
├── .gitignore
├── package.json                # 根package.json
├── nx.json                     # Nx配置
├── workspace.json              # 工作空间配置
└── README.md
```

## 开发工作流

### 本地开发设置

#### 前置条件

```bash
# 安装Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装Python 3.9+
sudo apt-get update
sudo apt-get install python3.9 python3.9-venv python3.9-dev

# 安装Docker和Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 安装Nx CLI
npm install -g nx@latest
```

#### 初始设置

```bash
# 克隆仓库
git clone https://github.com/your-org/colorectal-screening-model.git
cd colorectal-screening-model

# 安装依赖
npm install

# 设置Python虚拟环境
python3.11 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 复制环境变量
cp .env.example .env
# 编辑.env文件，填入必要的配置

# 启动开发数据库
docker-compose -f infrastructure/docker-compose/docker-compose.dev.yml up -d postgres redis minio

# 运行数据库迁移
nx run api-gateway:migrate

# 安装前端依赖
nx run web:install
```

#### 开发命令

```bash
# 启动所有服务
nx run-many --target=serve --all

# 启动前端应用
nx serve web

# 启动API网关
nx serve api-gateway

# 启动模拟服务
nx serve simulation-service

# 启动校准服务
nx serve calibration-service

# 运行测试
nx run-many --target=test --all

# 构建所有应用
nx run-many --target=build --all

# 代码检查
nx run-many --target=lint --all

# 类型检查
nx run-many --target=type-check --all
```

### 部署配置

#### 部署平台

**前端部署**：
- **平台**：Vercel / Netlify
- **构建命令**：`nx build web`
- **部署方法**：Git集成自动部署

**后端部署**：
- **平台**：Kubernetes集群 (AWS EKS / Google GKE)
- **构建命令**：Docker镜像构建
- **部署方法**：GitOps (ArgoCD)

#### CI/CD流水线

```yaml
# .github/workflows/ci.yaml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          npm ci
          pip install -r requirements.txt

      - name: Run tests
        run: |
          nx run-many --target=test --all --parallel

      - name: Run linting
        run: |
          nx run-many --target=lint --all --parallel

      - name: Type checking
        run: |
          nx run-many --target=type-check --all --parallel

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push images
        run: |
          nx run-many --target=docker-build --all --parallel
          nx run-many --target=docker-push --all --parallel

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - name: Deploy to staging
        run: |
          # 部署到测试环境的脚本
          ./scripts/deploy.sh staging

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
      - name: Deploy to production
        run: |
          # 部署到生产环境的脚本
          ./scripts/deploy.sh production
```

#### 环境配置

| 环境 | 前端URL | 后端URL | 用途 |
|------|---------|---------|------|
| 开发环境 | http://localhost:3000 | http://localhost:8000 | 本地开发 |
| 测试环境 | https://staging.colorectal-screening.org | https://api-staging.colorectal-screening.org | 预生产测试 |
| 生产环境 | https://colorectal-screening.org | https://api.colorectal-screening.org | 生产环境 |

## 测试策略

### 测试金字塔

```
                  E2E Tests
                 /        \
            Integration Tests
               /            \
          Frontend Unit  Backend Unit
```

### 测试组织

#### 前端测试

```
apps/web/tests/
├── unit/                      # 单元测试
│   ├── components/
│   │   ├── Button.test.tsx
│   │   ├── Chart.test.tsx
│   │   └── Form.test.tsx
│   ├── hooks/
│   │   ├── useSimulation.test.ts
│   │   └── useCalibration.test.ts
│   ├── services/
│   │   ├── api.test.ts
│   │   └── simulation.test.ts
│   └── utils/
│       ├── validation.test.ts
│       └── calculations.test.ts
├── integration/               # 集成测试
│   ├── pages/
│   │   ├── Dashboard.test.tsx
│   │   └── SimulationConfig.test.tsx
│   └── features/
│       ├── simulation-flow.test.tsx
│       └── data-upload.test.tsx
└── e2e/                      # 端到端测试
    ├── simulation-workflow.spec.ts
    ├── calibration-process.spec.ts
    └── results-analysis.spec.ts
```

#### 后端测试

```
apps/simulation-service/tests/
├── unit/                      # 单元测试
│   ├── core/
│   │   ├── test_population.py
│   │   ├── test_disease.py
│   │   └── test_screening.py
│   ├── services/
│   │   ├── test_project_service.py
│   │   └── test_simulation_service.py
│   └── utils/
│       ├── test_validation.py
│       └── test_calculations.py
├── integration/               # 集成测试
│   ├── test_api_endpoints.py
│   ├── test_database_operations.py
│   └── test_task_queue.py
└── performance/               # 性能测试
    ├── test_large_population.py
    ├── test_long_simulation.py
    └── test_concurrent_users.py
```

### 测试示例

#### 前端单元测试

```typescript
// apps/web/tests/unit/components/SimulationProgress.test.tsx
import { render, screen } from '@testing-library/react';
import { SimulationProgress } from '@/components/SimulationProgress';

describe('SimulationProgress', () => {
  it('displays correct progress percentage', () => {
    render(
      <SimulationProgress
        currentYear={50}
        totalYears={100}
        individualsProcessed={500000}
        totalIndividuals={1000000}
      />
    );

    expect(screen.getByText('50%')).toBeInTheDocument();
    expect(screen.getByText('Year 50 of 100')).toBeInTheDocument();
  });

  it('shows estimated completion time', () => {
    const estimatedCompletion = new Date(Date.now() + 3600000); // 1 hour from now

    render(
      <SimulationProgress
        currentYear={25}
        totalYears={100}
        estimatedCompletion={estimatedCompletion}
      />
    );

    expect(screen.getByText(/Estimated completion/)).toBeInTheDocument();
  });
});
```

#### 后端单元测试

```python
# apps/simulation-service/tests/unit/core/test_population.py
import pytest
from src.core.population import PopulationManager, Individual
from src.models.population import PopulationConfig

class TestPopulationManager:
    def test_initialize_population(self):
        config = PopulationConfig(
            size=1000,
            age_distribution={'min_age': 50, 'max_age': 80},
            gender_ratio=0.5
        )

        manager = PopulationManager(config)
        population = manager.initialize_population()

        assert len(population) == 1000
        assert all(50 <= individual.age <= 80 for individual in population)

        male_count = sum(1 for individual in population if individual.gender == 'male')
        female_count = len(population) - male_count

        # 允许5%的误差
        assert abs(male_count - female_count) <= 50

    def test_apply_mortality(self):
        config = PopulationConfig(size=1000)
        manager = PopulationManager(config)
        population = manager.initialize_population()

        initial_count = len(population)
        manager.apply_annual_mortality(population)

        # 应该有一些个体死亡
        assert len(population) < initial_count

        # 所有存活个体年龄应该增加1
        assert all(individual.age > 50 for individual in population)

    @pytest.mark.performance
    def test_large_population_performance(self):
        """测试大规模人群的性能"""
        import time

        config = PopulationConfig(size=100000)
        manager = PopulationManager(config)

        start_time = time.time()
        population = manager.initialize_population()
        end_time = time.time()

        # 10万人群初始化应该在5秒内完成
        assert end_time - start_time < 5.0
        assert len(population) == 100000
```

#### 集成测试

```python
# apps/simulation-service/tests/integration/test_simulation_workflow.py
import pytest
from httpx import AsyncClient
from src.main import app

@pytest.mark.asyncio
class TestSimulationWorkflow:
    async def test_complete_simulation_workflow(self):
        """测试完整的模拟工作流"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # 1. 创建项目
            project_data = {
                "name": "Test Simulation",
                "description": "Integration test simulation"
            }
            response = await client.post("/projects", json=project_data)
            assert response.status_code == 201
            project = response.json()
            project_id = project["id"]

            # 2. 配置模拟参数
            config_data = {
                "population": {
                    "size": 1000,
                    "age_distribution": {"min_age": 50, "max_age": 80}
                },
                "disease_model": {
                    "adenoma_pathway_rate": 0.85
                },
                "screening_strategy": {
                    "tools": [{
                        "type": "fit",
                        "start_age": 50,
                        "end_age": 75,
                        "interval": 2
                    }]
                }
            }
            response = await client.put(
                f"/projects/{project_id}/configuration",
                json=config_data
            )
            assert response.status_code == 200

            # 3. 启动模拟
            response = await client.post(f"/projects/{project_id}/simulations")
            assert response.status_code == 202
            simulation = response.json()
            simulation_id = simulation["id"]

            # 4. 检查模拟状态
            response = await client.get(f"/simulations/{simulation_id}/status")
            assert response.status_code == 200
            status = response.json()
            assert status["status"] in ["queued", "running"]
```

## 监控和可观测性

### 监控架构

```mermaid
graph TB
    subgraph "应用层"
        APP[应用服务]
        WEB[Web应用]
    end

    subgraph "指标收集"
        PROM[Prometheus]
        JAEGER[Jaeger]
        ELK[ELK Stack]
    end

    subgraph "可视化"
        GRAFANA[Grafana]
        KIBANA[Kibana]
    end

    subgraph "告警"
        ALERT[AlertManager]
        SLACK[Slack通知]
        EMAIL[邮件通知]
    end

    APP --> PROM
    APP --> JAEGER
    APP --> ELK
    WEB --> PROM

    PROM --> GRAFANA
    ELK --> KIBANA
    JAEGER --> GRAFANA

    PROM --> ALERT
    ALERT --> SLACK
    ALERT --> EMAIL
```

### 关键指标

#### 应用指标

```python
# 应用指标收集
from prometheus_client import Counter, Histogram, Gauge
import time

# 请求计数器
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status']
)

# 请求延迟
REQUEST_LATENCY = Histogram(
    'http_request_duration_seconds',
    'HTTP request latency',
    ['method', 'endpoint']
)

# 活跃模拟数量
ACTIVE_SIMULATIONS = Gauge(
    'active_simulations_total',
    'Number of active simulations'
)

# 模拟进度
SIMULATION_PROGRESS = Gauge(
    'simulation_progress_percent',
    'Simulation progress percentage',
    ['simulation_id']
)

# 中间件示例
@app.middleware("http")
async def metrics_middleware(request: Request, call_next):
    start_time = time.time()

    response = await call_next(request)

    # 记录指标
    REQUEST_COUNT.labels(
        method=request.method,
        endpoint=request.url.path,
        status=response.status_code
    ).inc()

    REQUEST_LATENCY.labels(
        method=request.method,
        endpoint=request.url.path
    ).observe(time.time() - start_time)

    return response
```

#### 业务指标

- **模拟性能指标**：
  - 模拟完成时间
  - 每秒处理的个体数量
  - 内存使用效率
  - GPU利用率

- **用户行为指标**：
  - 活跃用户数
  - 项目创建率
  - 模拟成功率
  - 功能使用频率

- **系统健康指标**：
  - API响应时间
  - 错误率
  - 数据库连接池状态
  - 队列长度

### 告警规则

```yaml
# Prometheus告警规则
groups:
  - name: simulation-service
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} requests per second"

      - alert: SimulationStuck
        expr: increase(simulation_progress_percent[30m]) == 0
        for: 30m
        labels:
          severity: warning
        annotations:
          summary: "Simulation appears to be stuck"
          description: "Simulation {{ $labels.simulation_id }} has not progressed in 30 minutes"

      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes / 1024 / 1024 / 1024 > 8
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}GB"

      - alert: DatabaseConnectionPoolExhausted
        expr: db_connection_pool_active >= db_connection_pool_max
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Database connection pool exhausted"
          description: "All database connections are in use"
```

### 日志管理

```python
# 结构化日志配置
import logging
import json
from datetime import datetime

class JSONFormatter(logging.Formatter):
    def format(self, record):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }

        # 添加额外字段
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        if hasattr(record, 'simulation_id'):
            log_entry['simulation_id'] = record.simulation_id
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id

        return json.dumps(log_entry)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('/var/log/app/application.log')
    ]
)

# 为每个服务配置专用logger
logger = logging.getLogger('simulation-service')
logger.addHandler(logging.StreamHandler())
logger.handlers[0].setFormatter(JSONFormatter())
```

---

**文档状态**：架构设计完成，等待技术评审和实施规划
